#!/bin/sh

set -e

# Function to print debug messages
debug() {
    echo "[DEBUG] $1"
}

# Note: Dependencies (PostgreSQL, Redis, MinIO) are handled by docker-compose depends_on
debug "Starting application - dependencies managed by docker-compose..."

# Print debug information
debug "Current directory: $(pwd)"
debug "Contents of current directory:"
ls -la

# Ensure necessary directories exist and have correct permissions
debug "Ensuring correct permissions for static directories..."
mkdir -p /home/<USER>/app/static /home/<USER>/app/staticfiles
chmod -R 755 /home/<USER>/app/static /home/<USER>/app/staticfiles

# Verify if the core module exists
if python -c "import core" 2>/dev/null; then
    debug "Module 'core' found"
else
    debug "ERROR: Module 'core' not found"
    debug "PYTHONPATH: $PYTHONPATH"
    debug "Contents of /home/<USER>/app:"
    ls -R /home/<USER>/app
fi

# Collect static files with timeout to prevent hanging
debug "Collecting static files..."
timeout 300 python manage.py collectstatic --noinput --clear -v 1 || {
    debug "Collectstatic timed out, trying without clearing..."
    timeout 180 python manage.py collectstatic --noinput -v 0
}

# Check the result of collectstatic
if [ $? -ne 0 ]; then
    debug "Error during collectstatic. Checking permissions:"
    ls -la /home/<USER>/app/staticfiles
    debug "Checking Django settings:"
    python -c "from django.conf import settings; print(f'STATIC_ROOT: {settings.STATIC_ROOT}')"
    python -c "from django.conf import settings; print(f'STATIC_URL: {settings.STATIC_URL}')"
    python -c "from django.conf import settings; print(f'STATICFILES_DIRS: {settings.STATICFILES_DIRS}')"
    exit 1
fi

# Run migrations
debug "Running migrations..."
python manage.py migrate

# Create superuser if environment variables are set
if [ -n "$DJANGO_SUPERUSER_USERNAME" ] && [ -n "$DJANGO_SUPERUSER_EMAIL" ] && [ -n "$DJANGO_SUPERUSER_PASSWORD" ]; then
    debug "Creating superuser..."
    python manage.py createsuperuser --noinput
fi

# Start Gunicorn
debug "Starting Gunicorn..."
exec gunicorn --bind 0.0.0.0:8000 core.wsgi:application
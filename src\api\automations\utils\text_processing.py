import re
from Levenshtein import distance, ratio
import unicodedata

"""
    Funciones para procesar textos
        - Normalizar
        - Distancia de Levenshtein
        - Similaridad
        
    Referencias:
    https://rapidfuzz.github.io/Levenshtein/levenshtein.html#distance
"""


def levenshtein_distance(s1: str, s2: str) -> int:
    """
    Calcula la distancia de Levenshtein entre dos cadenas usando la librería
    python-Levenshtein
    """
    return distance(s1, s2)


def similarity_ratio(s1: str, s2: str) -> float:
    """
    Calcula la similitud entre dos cadenas usando la librería python-Levenshtein
    """
    return ratio(s1, s2)


def normalize_text(text: str, lowercase: bool = True) -> str:
    """
    Normaliza el texto removiendo acentos, espacios extra y convirtiendo a minúsculas
    """
    if not text:
        return ""

    # Normalizar acentos
    text = unicodedata.normalize("NFD", text)
    text = re.sub(r"[\u0300-\u036f]", "", text)  # Eliminar diacríticos

    text = text.replace("ñ", "n").replace("Ñ", "N")

    # Remover espacios extra
    text = re.sub(r"\s+", " ", text.strip())

    if lowercase:
        text = text.lower()

    return text

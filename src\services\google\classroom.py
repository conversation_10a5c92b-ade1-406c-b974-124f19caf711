import re
from django.conf import settings
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

import logging

logger = logging.getLogger(__name__)


class GoogleClassroomManager:
    """
    Clase para gestionar invitaciones a Google Classroom utilizando la API de Google Classroom.
    """

    def __init__(self, credentials_file=None):
        """
        Inicializa el gestor de Google Classroom con las credenciales proporcionadas.

        Args:
            credentials_file (str): Ruta al archivo JSON de la Service Account
        """

        if credentials_file is None:
            credentials_file = f"{settings.SECRET_FILES_DIR}/credentials.json"

        self.SCOPES = [
            "https://www.googleapis.com/auth/classroom.courses",
            "https://www.googleapis.com/auth/classroom.rosters",
            "https://www.googleapis.com/auth/classroom.profile.emails",
        ]

        self.credentials = service_account.Credentials.from_service_account_file(
            credentials_file,
            scopes=self.SCOPES,
            subject="<EMAIL>",
        )
        self.service = build("classroom", "v1", credentials=self.credentials)

    def validate_gmail_email(self, email):
        """
        Valida que el correo electrónico sea una cuenta de Gmail válida.

        Args:
            email (str): Correo electrónico a validar

        Returns:
            bool: True si es un Gmail válido, False en caso contrario
        """
        if not email:
            return False

        # Patrón para validar email de Gmail
        gmail_pattern = r"^[a-zA-Z0-9._%+-]+@gmail\.com$"
        return bool(re.match(gmail_pattern, email.lower()))

    def get_course_info(self, course_id):
        """
        Obtiene información de un curso específico.

        Args:
            course_id (str): ID del curso de Google Classroom

        Returns:
            dict: Información del curso o None si hay error
        """
        try:
            course = self.service.courses().get(id=course_id).execute()
            return course
        except HttpError as error:
            print(f"Error al obtener información del curso: {error}")
            return None

    def get_course_by_enrollment_code(self, enrollment_code):
        """
        Busca un curso específico por su código de inscripción.

        Args:
            enrollment_code (str): Código de inscripción (class code)

        Returns:
            dict: Información del curso o None si no se encuentra
        """
        try:
            # Buscar en todos los cursos activos
            courses = self.list_courses(course_states=["ACTIVE"])

            for course in courses:
                if course.get("enrollmentCode") == enrollment_code:
                    return course

            # print(f"No se encontró curso con código: {enrollment_code}")
            return None

        except Exception as e:
            # print(f"Error al buscar curso por código: {e}")
            return None

    def list_courses(self, course_states=None):
        """
        Lista todos los cursos disponibles.

        Returns:
            list: Lista de cursos
        """
        try:
            params = {}

            if course_states:
                params["courseStates"] = course_states

            results = self.service.courses().list().execute()
            return results.get("courses", [])
        except HttpError as error:
            print(f"Error al listar cursos: {error}")
            return []

    def invite_student_to_course(self, course_id, student_email):
        """
        Envía una invitación a un estudiante para unirse a un curso.

        Args:
            course_id (str): ID del curso de Google Classroom
            student_email (str): Correo electrónico del estudiante (debe ser Gmail)

        Returns:
            dict: Resultado de la invitación o None si hay error
        """
        try:
            # Validar que sea un correo de Gmail
            if not self.validate_gmail_email(student_email):
                raise ValueError(
                    f"El correo {student_email} no es una cuenta de Gmail válida"
                )

            # Verificar que el curso existe
            course = self.get_course_info(course_id)
            if not course:
                raise ValueError(f"El curso con ID {course_id} no existe")

            # Crear la invitación
            invitation_body = {
                "userId": student_email,
                "courseId": course_id,
                "role": "STUDENT",
            }

            result = self.service.invitations().create(body=invitation_body).execute()

            print(
                f"Invitación enviada exitosamente a {student_email} para el curso {course_id}"
            )
            return result

        except HttpError as error:
            print(f"Error al enviar invitación: {error}")
            # Verificar si el usuario ya está en el curso
            if error.resp.status == 409:
                print(f"El usuario {student_email} ya está inscrito en el curso")
                return {"status": "already_enrolled", "message": "Usuario ya inscrito"}
            return None
        except ValueError as error:
            print(f"Error de validación: {error}")
            return None

    def list_course_students(self, course_id):
        """
        Lista todos los estudiantes de un curso.

        Args:
            course_id (str): ID del curso

        Returns:
            list: Lista de estudiantes del curso
        """
        try:
            results = (
                self.service.courses().students().list(courseId=course_id).execute()
            )
            return results.get("students", [])
        except HttpError as error:
            print(f"Error al listar estudiantes del curso: {error}")
            return []

    def list_pending_invitations(self, course_id=None):
        """
        Lista las invitaciones pendientes.

        Args:
            course_id (str, optional): ID del curso específico

        Returns:
            list: Lista de invitaciones pendientes
        """
        try:
            params = {}
            if course_id:
                params["courseId"] = course_id

            results = self.service.invitations().list(**params).execute()
            return results.get("invitations", [])
        except HttpError as error:
            print(f"Error al listar invitaciones pendientes: {error}")
            return []

    def delete_invitation(self, invitation_id):
        """
        Elimina una invitación pendiente.

        Args:
            invitation_id (str): ID de la invitación

        Returns:
            bool: True si la eliminación fue exitosa, False en caso contrario
        """
        try:
            self.service.invitations().delete(id=invitation_id).execute()
            return True
        except HttpError as error:
            print(f"Error al eliminar invitación: {error}")
            return False

    def check_student_enrollment_status(self, course_id, student_email):
        """
        Verifica el estado de inscripción de un estudiante en un curso.

        Args:
            course_id (str): ID del curso
            student_email (str): Correo del estudiante

        Returns:
            dict: Estado de inscripción del estudiante
        """
        try:
            # Verificar si está inscrito como estudiante
            students = self.list_course_students(course_id)
            for student in students:
                if student.get("profile", {}).get("emailAddress") == student_email:
                    return {"status": "enrolled", "data": student}

            # Verificar si tiene invitación pendiente
            invitations = self.list_pending_invitations(course_id)
            for invitation in invitations:
                if invitation.get("userId") == student_email:
                    return {"status": "invited", "data": invitation}

            return {"status": "not_enrolled", "data": None}

        except HttpError as error:
            print(f"Error al verificar estado de inscripción: {error}")
            return {"status": "error", "data": None}

    def remove_student_from_course(self, course_id, student_email):
        """
        Retira a un estudiante de un curso de Google Classroom.

        Args:
            course_id (str): ID del curso de Google Classroom
            student_email (str): Correo electrónico del estudiante

        Returns:
            dict: Resultado de la operación
        """
        try:
            # Primero verificar el estado del estudiante
            enrollment_status = self.check_student_enrollment_status(
                course_id, student_email
            )

            if enrollment_status["status"] == "enrolled":
                # Si está inscrito, obtener el userId del estudiante
                student_data = enrollment_status["data"]
                user_id = student_data.get("userId")

                if user_id:
                    # Remover al estudiante del curso
                    self.service.courses().students().delete(
                        courseId=course_id, userId=user_id
                    ).execute()

                    logger.info(
                        f"Estudiante {student_email} removido exitosamente del curso {course_id}"
                    )

                    return {
                        "status": "success",
                        "message": f"Estudiante {student_email} removido del curso",
                        "action": "removed",
                    }
                else:
                    return {
                        "status": "error",
                        "message": "No se pudo obtener el ID del usuario",
                        "action": "none",
                    }

            elif enrollment_status["status"] == "invited":
                # Si tiene una invitación pendiente, cancelar la invitación
                invitation_data = enrollment_status["data"]
                invitation_id = invitation_data.get("id")

                if invitation_id and self.delete_invitation(invitation_id):
                    logger.info(
                        f"Invitación cancelada para {student_email} en el curso {course_id}"
                    )
                    return {
                        "status": "success",
                        "message": f"Invitación cancelada para {student_email}",
                        "action": "invitation_cancelled",
                    }
                else:
                    return {
                        "status": "error",
                        "message": "No se pudo cancelar la invitación",
                        "action": "none",
                    }

            elif enrollment_status["status"] == "not_enrolled":
                return {
                    "status": "success",
                    "message": f"El estudiante {student_email} no está inscrito en el curso",
                    "action": "none",
                }
            else:
                return {
                    "status": "error",
                    "message": "Error al verificar el estado del estudiante",
                    "action": "none",
                }

        except HttpError as error:
            error_message = f"Error al remover estudiante: {error}"
            logger.error(error_message)
            # Manejar errores específicos
            if error.resp.status == 404:
                return {
                    "status": "error",
                    "message": "Curso o estudiante no encontrado",
                    "action": "none",
                }
            elif error.resp.status == 403:
                return {
                    "status": "error",
                    "message": "Sin permisos para remover al estudiante",
                    "action": "none",
                }
            else:
                return {"status": "error", "message": error_message, "action": "none"}
        except Exception as e:
            error_message = f"Error inesperado: {e}"
            logger.error(error_message)
            return {"status": "error", "message": error_message, "action": "none"}

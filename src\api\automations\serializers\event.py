from rest_framework import serializers
from phonenumbers import NumberParseException, geocoder
import phonenumbers
from core.models import (
    EventSchedule,
    EventScheduleEnrollment,
    Partnership,
    Major,
    User as Contact,
    EducationalInstitution,
)
from api.automations.utils.educational_institution import (
    find_best_match,
    find_with_django_filter,
)
from api.automations.utils.event import (
    map_interest_to_specialization_levenshtein,
    map_acronym_to_ceus_option,
    map_diffusion_channel,
)

from api.automations.utils.text_processing import normalize_text


class AutomationsEventEnrollmentSerializer(serializers.ModelSerializer):
    event_schedule = serializers.UUIDField()
    first_name = serializers.Char<PERSON>ield(max_length=127)
    last_name = serializers.Char<PERSON>ield(max_length=128, required=False, allow_blank=True)
    email = serializers.EmailField(max_length=128)
    phone_number = serializers.Char<PERSON><PERSON>(
        max_length=32,
        required=True,
    )
    occupation = serializers.<PERSON>r<PERSON><PERSON>(
        max_length=64,
        allow_blank=True,
        allow_null=True,
    )
    major = serializers.CharField(required=False, allow_blank=True)
    university = serializers.CharField(required=False, allow_blank=True)
    interests = serializers.Char<PERSON>ield(max_length=500, required=False, allow_blank=True)
    diffusion_channel = serializers.CharField(
        max_length=64, required=False, allow_blank=True
    )
    ceu_to_apply = serializers.CharField(max_length=64, required=False, allow_null=True)

    class Meta:
        model = EventScheduleEnrollment
        fields = [
            "event_schedule",
            "first_name",
            "last_name",
            "email",
            "phone_number",
            "occupation",
            "major",
            # "term", Obviado para automatizaciones, el formulario de meta usa rangos
            "university",
            "interests",
            "diffusion_channel",
            "ceu_to_apply",
        ]

    def validate_event_schedule(self, value):
        """
        Validate that the event_schedule UUID corresponds to an existing EventSchedule.
        """
        try:
            EventSchedule.objects.get(esid=value)
        except EventSchedule.DoesNotExist:
            raise serializers.ValidationError(
                "Event schedule with this UUID does not exist."
            )
        return value

    def validate_ceu_to_apply(self, value):
        """
        Validate that the ceu_to_apply UUID corresponds to an existing User.
        """
        if value:
            try:
                mapped_option = map_acronym_to_ceus_option(value)
                if mapped_option:
                    return mapped_option
                return None
            except Exception as e:
                print(f"Error mapping ceu_to_apply: {value} - {e}")
                return None

        return value

    def validate_diffusion_channel(self, value):
        """
        Validate that the diffusion_channel is a valid option.
        """
        if value:
            return map_diffusion_channel(value)
        return value

    def validate_occupation(self, value):
        """
        Validate occupation against Contact.OCUPATION_CHOICES.
        """
        if value:
            valid_occupations = [choice[0] for choice in Contact.OCUPATION_CHOICES]
            if value not in valid_occupations:
                raise serializers.ValidationError(
                    f"Invalid occupation. Must be one of: "
                    f"{', '.join(valid_occupations)}"
                )
        return value

    def validate_major(self, value):
        """
        Validate major exists in database and return the name.

            value is a string with the major name, we need to find the object case insensite
            ejemplo :_ economía se tiene que normalizar a economia y buscar con django usando también normalización
        """
        if value:
            try:
                normalized_value = normalize_text(value)
                # buscar valor exacto case insensitive
                major = Major.objects.filter(name__iexact=normalized_value).first()
                if major:
                    return major.name

                # No match found
                return None
            except Major.DoesNotExist:
                raise serializers.ValidationError(
                    "Major does not exist in our database."
                )
        return value

    def validate_university(self, value):
        """
        Validate university exists in database and return the name.
        """
        if value:
            try:
                first_match = find_with_django_filter(value)
                if first_match:
                    return first_match.name

                # No match found - search by Levenshtein
                second_match = find_best_match(value, confidence_threshold=0.85)
                if second_match:
                    return second_match.name

                # No match found - return None
                return None
            except EducationalInstitution.DoesNotExist:
                raise serializers.ValidationError(
                    "University does not exist in our database."
                )
        return value

    def validate_partnership(self, value):
        """
        Validate partnership exists if provided (relaxed validation).
        If value looks like UUID, search by pid; otherwise search by short_pid.
        """
        if value:
            try:
                import uuid

                # Check if value looks like a UUID
                try:
                    uuid.UUID(str(value))
                    is_uuid = True
                except ValueError:
                    is_uuid = False

                if is_uuid:
                    # Value is UUID, search by pid
                    partnership = Partnership.objects.filter(pid=value).first()
                    if partnership:
                        return value
                else:
                    # Value is not UUID, search by short_pid
                    partnership = Partnership.objects.filter(short_pid=value).first()
                    if partnership:
                        return partnership.pid  # Return the actual pid

                # If not found, return None (relaxed validation)
                return None
            except Exception:
                # If any error occurs, just return None
                return None
        return value

    def validate_interests(self, value):
        """
        Convierte los intereses en una lista mapeada a las opciones oficiales usando Levenshtein.
        """
        if not value:
            return []

        # Si es string, dividir por coma y limpiar
        if isinstance(value, str):
            interests_list = [interest.strip() for interest in value.split(",")]
            interests_list = [interest for interest in interests_list if interest]
        elif isinstance(value, list):
            if not all(isinstance(item, str) for item in value):
                raise serializers.ValidationError("All interests must be strings.")
            interests_list = value
        else:
            raise serializers.ValidationError(
                "Interests must be a string (comma-separated) or an array of strings."
            )

        # Mapear cada interés usando la función de Levenshtein
        mapped_interests = []
        for interest in interests_list:
            mapped = map_interest_to_specialization_levenshtein(interest)
            if mapped:
                mapped_interests.append(mapped)
            # Si no hay match, no incluir
        return mapped_interests

    def validate_phone_number(self, value):
        """
        Validate phone number format using phonenumbers library.
        """
        try:
            # If have 9 digits and starts with 9, assume it's a local number (Perú)
            if len(value) == 9 and value.startswith("9"):
                value = f"+51{value}"
            # Parse the phone number
            parsed_number = phonenumbers.parse(value, None)

            # Check if the number is valid
            if not phonenumbers.is_valid_number(parsed_number):
                raise serializers.ValidationError(
                    "Enter a valid phone number in international format."
                )

            # Extract country information
            country_code = phonenumbers.region_code_for_number(parsed_number)
            country_name = geocoder.description_for_number(parsed_number, "en")

            # Print country information to console
            print(f"Phone validation - Country: {country_name} ({country_code})")
            formatted_number = phonenumbers.format_number(
                parsed_number, phonenumbers.PhoneNumberFormat.INTERNATIONAL
            )
            print(f"Formatted number: {formatted_number}")

        except NumberParseException as e:
            error_messages = {
                NumberParseException.INVALID_COUNTRY_CODE: "Invalid country code.",
                NumberParseException.NOT_A_NUMBER: "The phone number is not valid.",
                NumberParseException.TOO_SHORT_NSN: "The phone number is too short.",
                NumberParseException.TOO_SHORT_AFTER_IDD: (
                    "The phone number is too short after IDD."
                ),
                NumberParseException.TOO_LONG: "The phone number is too long.",
            }
            error_message = error_messages.get(
                e.error_type, "Invalid phone number format."
            )
            raise serializers.ValidationError(
                f"{error_message} Please use international format."
            )

        return value

    def create(self, validated_data):
        """
        Create enrollment and handle contact creation/linking.
        """
        email = validated_data.get("email")
        phone_number = validated_data.get("phone_number")

        contact = None
        has_contact = False
        needs_conciliation = False

        # Try to create or find contact based on email and phone number
        try:
            contact = Contact.objects.filter(
                email=email, phone_number=phone_number, deleted=False
            ).first()

            if contact:
                has_contact = True
            else:
                # Check if email OR phone exists separately (partial match)
                email_exists = Contact.objects.filter(
                    email=email, deleted=False
                ).exists()
                phone_exists = Contact.objects.filter(
                    phone_number=phone_number, deleted=False
                ).exists()

                if email_exists or phone_exists:
                    # Partial match found - needs conciliation
                    needs_conciliation = True
                    contact = None
                else:
                    # No match found - create new contact
                    contact = self._create_new_contact(validated_data)
                    has_contact = False
        except Exception as e:
            # Just log the error
            print(f"Error creating or finding contact: {e}")

        # Get partnership if provided
        partnership_obj = None
        partnership_value = validated_data.get("partnership")
        if partnership_value:
            try:
                import uuid

                # Check if value looks like a UUID
                try:
                    uuid.UUID(str(partnership_value))
                    is_uuid = True
                except ValueError:
                    is_uuid = False

                if is_uuid:
                    # Value is UUID, search by pid
                    partnership_obj = Partnership.objects.filter(
                        pid=partnership_value
                    ).first()
                else:
                    # Value is not UUID, search by short_pid
                    partnership_obj = Partnership.objects.filter(
                        short_pid=partnership_value
                    ).first()
            except Exception:
                # If any error occurs, partnership_obj remains None
                partnership_obj = None

        # Get event schedule
        event_schedule = EventSchedule.objects.get(
            esid=validated_data["event_schedule"]
        )

        # Process interests
        interests = self.validate_interests(validated_data.get("interests", ""))
        ceu_to_apply = validated_data.get("ceu_to_apply", None)

        # If is already enrolled, only update the data
        existing_enrollment = None

        # 1. Search by phone number
        existing_enrollment = EventScheduleEnrollment.objects.filter(
            event_schedule=event_schedule,
            phone_number=phone_number,
        ).first()

        if not existing_enrollment:
            # 2. Search by email
            existing_enrollment = EventScheduleEnrollment.objects.filter(
                event_schedule=event_schedule,
                email=email,
            ).first()

        if existing_enrollment:
            # Update existing enrollment data
            update_fields = {
                "user": contact,
                "first_name": validated_data.get("first_name", ""),
                "last_name": validated_data.get("last_name", ""),
                "email": validated_data.get("email", ""),
                "phone_number": phone_number,
                "occupation": validated_data.get("occupation", ""),
                "major": validated_data.get("major", ""),
                "university": validated_data.get("university", ""),
                "interests": interests,
                "diffusion_channel": validated_data.get("diffusion_channel", ""),
                "has_contact": has_contact,
                "needs_conciliation": needs_conciliation,
                "partnership": partnership_obj,
                "ceu_to_apply": ceu_to_apply,
            }

            EventScheduleEnrollment.objects.filter(pk=existing_enrollment.pk).update(
                **update_fields
            )

            # Refresca el objeto desde la base de datos
            existing_enrollment.refresh_from_db()

            return existing_enrollment

        # Create enrollment
        enrollment = EventScheduleEnrollment.objects.create(
            event_schedule=event_schedule,
            user=contact,
            first_name=validated_data.get("first_name", ""),
            last_name=validated_data.get("last_name", ""),
            email=validated_data.get("email", ""),
            phone_number=phone_number,
            occupation=validated_data.get("occupation", ""),
            major=validated_data.get("major", ""),
            # term=validated_data.get("term", ""),
            university=validated_data.get("university", ""),
            interests=interests,
            diffusion_channel=validated_data.get("diffusion_channel", ""),
            has_contact=has_contact,
            needs_conciliation=needs_conciliation,
            partnership=partnership_obj,
            ceu_to_apply=ceu_to_apply,
        )

        return enrollment

    def _create_new_contact(self, validated_data):
        """
        Helper method to create a new contact.
        """
        # Get foreign key objects by name (since validated_data now contains names)
        major_obj = None
        # term_obj = None
        university_obj = None

        if validated_data.get("major"):
            major_obj = Major.objects.filter(name=validated_data["major"]).first()

        # if validated_data.get("term"):
        #     term_obj = Term.objects.filter(name=validated_data["term"]).first()

        if validated_data.get("university"):
            university_obj = EducationalInstitution.objects.filter(
                name=validated_data["university"]
            ).first()

        # Create username from phone number or email
        phone_number = validated_data.get("phone_number")
        email = validated_data.get("email")

        if phone_number:
            base_username = phone_number
        elif email:
            base_username = email.split("@")[0]
        else:
            base_username = "user"

        username = base_username
        counter = 1

        while Contact.objects.filter(username=username).exists():
            username = f"{base_username}_{counter}"
            counter += 1

        # Create new contact
        contact = Contact.objects.create(
            username=username,
            first_name=validated_data.get("first_name", ""),
            last_name=validated_data.get("last_name", ""),
            email=validated_data.get("email", ""),
            phone_number=phone_number or "",
            ocupation=validated_data.get("occupation", ""),
            major=major_obj,
            # term=term_obj,
            educational_institution=university_obj,
            is_active=False,
        )

        return contact

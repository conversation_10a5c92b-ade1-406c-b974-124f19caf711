from django.conf import settings
from django.urls import include, path, re_path
from django.conf.urls.static import static
from rest_framework import routers, permissions
from drf_yasg import openapi
from drf_yasg.views import get_schema_view
from api.views import HealthCheckView

router = routers.DefaultRouter(trailing_slash=False)

schema_view = get_schema_view(
    openapi.Info(
        title="CEU Portals API",
        default_version="v1",
        description="API documentation for CEU Portals API project",
        terms_of_service="https://www.google.com/policies/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    public=True,
    permission_classes=(permissions.IsAuthenticated,),
)

urlpatterns = (
    [
        re_path(
            r"^swagger(?P<format>\.json|\.yaml)$",
            schema_view.without_ui(cache_timeout=0),
            name="schema-json",
        ),
        path(
            "swagger/",
            schema_view.with_ui("swagger", cache_timeout=0),
            name="schema-swagger-ui",
        ),
        path(
            "redoc/",
            schema_view.with_ui("redoc", cache_timeout=0),
            name="schema-redoc",
        ),
        path("website/", include("api.website.urls")),
        path("shared/", include("api.shared.urls")),
        path("cms/", include("api.cms.urls")),
        path("erp/", include("api.erp.urls")),
        path("crm/", include("api.crm.urls")),
        path("lms/", include("api.lms.urls")),
        path("classroom/", include("api.classroom.urls")),
        path("documents/", include("api.documents.urls")),
        path("automations/", include("api.automations.urls")),
        path("hcz", HealthCheckView.as_view(), name="health-check"),
    ]
    + static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    + router.urls
)

# CEU-Portals Backend

Backend (API) de CEU Centro de Especialización, construido con **Django + Django REST Framework** y ejecutado en Docker (PostgreSQL, Redis, Celery, MinIO).

---

## 📦 Arquitectura (resumen)

- **API (portals-api):** Django, expuesto en `localhost:8001`
- **DB (portals-psql):** PostgreSQL 15, expuesto en `localhost:5432`
- **Cache/cola (portals-redis):** Redis 7, expuesto en `localhost:6379`
- **Tareas (portals-celery, portals-celery-beat):** Celery worker y scheduler
- **Archivos (portals-storage):** MinIO, expuesto en `localhost:9000` (console `9001`)

El archivo de orquestación está en: `installation/docker-compose.dev.yml`.

---

## ✅ Requisitos previos

Antes de comenzar con la instalación y ejecución del proyecto, es importante verificar lo siguiente:


- **WSL2 (Windows Subsystem for Linux 2)**:  
  Si estás en Windows, asegúrate de tener instalado y habilitado **WSL2**.  
  Esto es necesario para que Docker Desktop funcione correctamente en Windows, ya que utiliza WSL2 como backend.  

- `make migrate`
- `make sync-permissions` *(create custom permissions)*
- `make create-superuser`
- `make db_seed` *(seed initial data to DB)*


  👉 Puedes instalarlo ejecutando en PowerShell (como administrador):  
  ```powershell
  wsl --install
  ```
- Docker Desktop
- Docker Compose V2 (viene con Docker Desktop)
- Git
- (Opcional) `make` para atajos, si tu entorno lo soporta

Verifica versiones:

```sh
docker --version
docker compose version
git --version
```

---

## 🚀 Inicio rápido (primera vez)

1. **Clonar el repositorio**
    ```sh
    git clone <URL_DEL_REPO> ceu-portals
    cd ceu-portals
    ```

2. **Variables de entorno**

    En la consola introduce
    ```sh
    cp .env.example .env
    ```


    > **Nota:** No necesitas venv/conda para desarrollo si usas Docker. La API corre dentro del contenedor.

3. **Levantar servicios**
    ```sh
    docker compose -f installation/docker-compose.dev.yml up -d
    ```

    Comprueba que estén arriba:
    ```sh
    docker ps
    ```

    Deberías ver: `portals-api`, `portals-psql`, `portals-redis`, `portals-celery`, `portals-celery-beat`, `portals-storage`.

4. **Migraciones + permisos + superusuario**
    ### Recuerda hacer todo en consola de Wsl
    ```sh
    # aplicar migraciones
    docker exec -it portals-api python manage.py migrate

    # sincronizar permisos personalizados (roles/acciones)
    docker exec -it portals-api python manage.py sync_permissions

    # crear superusuario
    docker exec -it portals-api python manage.py createsuperuser
    ```

5. **Probar la API**
    ```sh
    # listado de programas (paginado)
    curl http://localhost:8001/api/v1/website/offerings
    ```

---

## 🧪 Datos de ejemplo (semilla mínima)

Para que el frontend muestre “programas” (`offerings`) en `/api/v1/website/offerings`, el estado debe ser `LAUNCHED`.

Abre el shell de Django:

```sh
docker exec -it portals-api python manage.py shell
```

Creamos una insercion para tener datos de prueba en nuestras tablas mas importantes:

```python
from datetime import date, timedelta
from decimal import Decimal
from django.utils import timezone
from django.contrib.auth import get_user_model
from core.models import Instructor, Order, Payment, Event, EventSchedule
from core.models.offering import Offering

# Asegurar un usuario dueño de la orden (usa id=1 si existe; si no, crea uno de demo)
User = get_user_model()
owner = User.objects.filter(id=1).first()
if owner is None:
    owner = User.objects.create_user(
        username="demo_user",
        email="<EMAIL>",
        password="demo1234"
    )
    print("✅ Usuario creado:", owner.id, owner.username)
else:
    print("ℹ️ Usando usuario existente:", owner.id, owner.username)

# 1) Instructor (usa campos reales del modelo)
instructor = Instructor.objects.create(
    full_name="Juan Pérez",
    biography="Instructor de programación con 10 años de experiencia.",
    title="M. Sc. en Ciencias de la Computación",
    highlighted_info="Ha capacitado a más de 500 estudiantes.",
    facebook_url="https://facebook.com/juanperez",
    linkedin_url="https://linkedin.com/in/juanperez",
    instagram_url="https://instagram.com/juanperez",
)
print("✅ Instructor creado:", instructor.iid, instructor.full_name)

# 2) Orden (Order requiere 'owner')
order = Order.objects.create(
    owner=owner
)
print("✅ Orden creada:", order.oid, "owner:", order.owner_id)

# 3) Pago (Payment)
payment = Payment.objects.create(
    order=order,
    amount=Decimal("100.00"),
    is_paid=True,
    payment_date=timezone.now(),
)
print("✅ Pago creado:", payment.pid, "monto:", payment.amount, "pagado:", payment.is_paid)

# 4) Evento (Event) — usa 'name' y 'slug' en este proyecto
event = Event.objects.create(
    slug="curso-python",  # si ya existe, cambia el slug para evitar conflicto único
    name="Curso de Python Intensivo",
    description="Evento introductorio de Python para principiantes.",
    instructor=instructor,
    price=Decimal("200.00"),
)
print("✅ Evento creado:", event.eid, event.name)

# 5) Programación de Evento (EventSchedule) — short_esid máx 8 chars
event_schedule = EventSchedule.objects.create(
    short_esid="PY001",
    event=event,
    description="Primera sesión del curso intensivo de Python.",
    start_date=timezone.now(),
    end_date=timezone.now() + timezone.timedelta(hours=2),
)
print("✅ Programación de evento creada:", event_schedule.esid, event_schedule.short_esid)

# 6) Programa (Offering)
o = Offering.objects.create(
    name="Curso de Prueba",
    slug="curso-prueba",  # si ya existe, usa otro slug único
    description="Este es un curso de prueba",
    start_date=date.today(),
    end_date=date.today() + timedelta(days=30),
    hours=40,
    modality="REMOTE",          # REMOTE | IN_PERSON
    type="SPECIALIZATION",      # SPECIALIZATION | PREPARATION | REVIEW_WORKSHOP | UNDERGRADUATE_FORMATION
    stage="LAUNCHED",           # PLANNING | LAUNCHED | ENROLLMENT | ENROLLMENT_CLOSED | FINISHED
    format="LIVE",              # LIVE | ASYNCHRONOUS
    base_price=Decimal("100.00"),
    foreign_base_price=Decimal("100.00"),
    discount=Decimal("0.00"),
)
print("✅ Programa creado:", o.oid, o.name)

```

Valida por HTTP:

```sh
curl "http://localhost:8001/admin/"
```

Alli puedes iniciar sesion con tu superusuario creado.

--- 
## 🏷️ Creación de grupos requeridos

Accede al panel de administración de Django en [http://localhost:8001/admin/auth/group/add/](http://localhost:8001/admin/auth/group/add/).

Debes crear los siguientes grupos exactamente con estos nombres:
- `management`
  
  > Al crear el grupo **management**, en la sección de "Permissions" (Permisos), selecciona **todos los permisos disponibles**.  
Para hacerlo rápidamente, haz clic en el botón **"Choose all"** (Seleccionar todo) que aparece sobre la lista de permisos.

- `commercial`
- `marketing`
- `students`

> **Recomendación:** Crea cada grupo asegurándote de escribir el nombre correctamente.

### 2. Asignar los grupos al superusuario

Una vez creados los grupos, debes asignarlos a tu superusuario para que tenga acceso completo.

1. Ve a la sección de usuarios en el panel de administración:  
   [http://localhost:8001/admin/core/user/?q=username](http://localhost:8001/admin/core/user/?q=username)  
   (Reemplaza `<username>` por el nombre de usuario que usaste al crear el superusuario).

2. Haz clic en el nombre de tu superusuario para entrar al detalle de su perfil.

3. En la sección "Groups" (Grupos), selecciona **todos los grupos** que creaste:  
   - commercial  
   - management  
   - marketing  
   - students

4. Guarda los cambios haciendo clic en **"Save"** en la parte inferior de la página.

### 3. Verificar acceso en el frontend

- Recarga la página del frontend.
- Inicia sesión en [http://localhost:3000/login](http://localhost:3000/login) usando tu superusuario.
- Si los pasos anteriores fueron correctos, deberías ver **todos los módulos y submódulos** habilitados en la interfaz.

---

## 🗄️ Creación y configuración de buckets en MinIO

MinIO se utiliza para almacenar archivos y recursos del sistema. Debes crear dos buckets principales: `private` y `public`.

### 1. Acceder a la consola de MinIO

- Abre [http://localhost:9001/login](http://localhost:9001/login) en tu navegador.

### 2. Iniciar sesión con las credenciales de entorno

- **Usuario:** Usa el valor de la variable de entorno `MINIO_ACCESS_KEY`.
- **Contraseña:** Usa el valor de la variable de entorno `MINIO_SECRET_KEY`.

Estas credenciales se encuentran en el archivo [`.env`](./.env) del proyecto.

### 3. Crear los buckets necesarios

- Una vez dentro de la consola de MinIO, busca el botón **"Create bucket"** (Crear bucket).
  
- Crea un bucket llamado **`private`** y otro llamado **`public`**.
  
- En el bucket [`public`](http://localhost:9001/buckets/public/admin/summary), es necesario modificar la política de acceso para permitir acceso público.

> **Recomendación:** Usa nombres en minúsculas y sin espacios para evitar problemas de compatibilidad.

---

## 🔧 Comandos útiles (Docker)

- **Django shell**
    ```sh
    docker exec -it portals-api python manage.py shell
    ```

- **Makemigrations / migrate**
    ```sh
    docker exec -it portals-api python manage.py makemigrations
    docker exec -it portals-api python manage.py migrate
    ```

- **Sincronizar permisos**
    ```sh
    docker exec -it portals-api python manage.py sync_permissions
    ```

- **Superusuario**
    ```sh
    docker exec -it portals-api python manage.py createsuperuser
    ```

- **Logs**
    ```sh
    docker logs -f portals-api
    ```

- **Entrar a la DB (psql)**
    ```sh
    docker exec -it portals-psql psql -U portals -d portals-db
    ```

---

## 🛑 Parar, reiniciar y limpiar

- **Apagar todo**
    ```sh
    docker compose -f installation/docker-compose.dev.yml down
    ```

- **Recrear con build**
    ```sh
    docker compose -f installation/docker-compose.dev.yml up -d --build
    ```

- **Eliminar volúmenes (⚠ elimina datos)**
    ```sh
    docker compose -f installation/docker-compose.dev.yml down -v
    ```

---

## 🧭 Rutas y puertos por defecto

- **API:** [http://localhost:8001/](http://localhost:8001/)
- **Offerings (Website):** `GET /api/v1/website/offerings`
- **Admin Django:** [http://localhost:8001/admin/](http://localhost:8001/admin/)
- **MinIO (S3):** [http://localhost:9000](http://localhost:9000) (console: [http://localhost:9001](http://localhost:9001))

---

## 🤝 Integración con Frontend (referencia rápida)

Si el frontend corre en `localhost:3000`, asegúrate que `.env` tenga:

```env
CORS_ALLOWED_ORIGINS="http://localhost:3000,http://localhost:4000"
ALLOWED_HOSTS="localhost"
```

Y en el frontend, usar:

```env
API_BASE_URL=http://localhost:8001
NEXT_PUBLIC_API_BASE_URL=http://localhost:8001
```

Si accedes desde otra máquina en la red local, cambia `localhost` por la IP de tu host (p. ej. `http://************:8001`) y añade esa IP a `ALLOWED_HOSTS` y a `CORS`.

---

## 🧰 (Opcional) Uso de Makefile

Si tu repo incluye targets de make, puedes tener atajos como:

```sh
make migrate             # ejecuta migrate dentro del contenedor
make sync-permissions    # sincroniza permisos
make create-superuser    # crea superusuario
```

Si un target no existe (ej. `make env` o `make resources`), usa directamente los comandos `docker exec ...` mostrados arriba.

---

## 🛠️ Solución de problemas

- **curl http://localhost:8001/... no responde**
    - Verifica que `portals-api` está corriendo: `docker ps`
    - Revisa logs: `docker logs -f portals-api`

- **401 Unauthorized al iniciar sesión**
    - Crea un superusuario y prueba autenticación.
    - Si es un registro por API, revisa payload y validaciones.

- **No aparecen programas en /website/offerings**
    - El `stage` debe ser `LAUNCHED`.
    - Verifica con `docker exec -it portals-api python manage.py shell`.

- **Error de CORS desde frontend**
    - Agrega el origin del front en `CORS_ALLOWED_ORIGINS`.
    - Reinicia la API si cambiaste `.env`.

- **Puertos ocupados**
    - Cambia mapeos en `installation/docker-compose.dev.yml` o libera puertos.

---

## 🗂️ Tablas clave

- `core_offering` — Programas/Ofertas académicas
- `core_instructor` — Instructores
- `core_order`, `core_orderitem`, `core_payment` — Órdenes y pagos
- `core_event`, `core_eventschedule` — Eventos y programación
- `core_user` — Usuarios

Listar tablas en psql:

```sql
\dt
SELECT * FROM core_offering LIMIT 5;
```

---

## 🧩 Permisos personalizados

Sincroniza permisos luego de migraciones o cambios en reglas:

```sh
docker exec -it portals-api python manage.py sync_permissions
```

Los permisos están organizados por módulo (CRM, CMS, LMS, ERP) y usan el patrón:

- **codename:** `<action>_<module>_<resource>`
- **name:** `<MODULE> | Can <action> <resource>`

---

## 📄 Licencia y equipo

**Tech Lead:** @reqhiem

Mantén este README actualizado para nuevos integrantes del equipo.

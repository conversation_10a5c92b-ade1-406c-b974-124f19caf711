from rest_framework import viewsets
from core.models import Order
from api.crm.serializers.order import (
    CrmOrderSerializer,
    CrmCreateOrderSerializer,
    CrmRetrieveOrderSerializer,
    CrmUpdateOrderSerializer,
)
from api.crm.filters.order import CrmOrderFilter
from api.paginations import StandardResultsPagination
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated, DjangoModelPermissions
from api.permissions import IsStaffUser, CanModifySoldOrderStage
from api.mixins import AuditMixin, SwaggerTagMixin
from rest_framework.response import Response
from rest_framework import status
from api.crm.tasks.classroom import process_classroom_invitation_for_order
from rest_framework.decorators import action
from django.db import transaction


class CrmOrderViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = Order
    queryset = Order.objects.filter(deleted=False).order_by("-created_at")
    serializer_class = CrmOrderSerializer
    swagger_tags = ["Order"]
    pagination_class = StandardResultsPagination

    authentication_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated & IsStaffUser & DjangoModelPermissions & CanModifySoldOrderStage
    ]

    filterset_class = CrmOrderFilter
    ordering_fields = ["stage", "created_at", "updated_at", "total"]

    def get_serializer(self, *args, **kwargs):
        if self.action == "create":
            return CrmCreateOrderSerializer(
                *args, context=self.get_serializer_context(), **kwargs
            )
        elif self.action == "retrieve":
            return CrmRetrieveOrderSerializer(*args, **kwargs)
        elif self.action in ["update", "partial_update"]:
            return CrmUpdateOrderSerializer(
                *args, context=self.get_serializer_context(), **kwargs
            )
        return super().get_serializer(*args, **kwargs)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        retrieve_serializer = CrmRetrieveOrderSerializer(serializer.instance)
        return Response(retrieve_serializer.data, status=status.HTTP_201_CREATED)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        retrieve_serializer = CrmRetrieveOrderSerializer(serializer.instance)
        return Response(retrieve_serializer.data)

    def destroy(self, request, pk=None):
        order = self.get_object()

        with transaction.atomic():
            for item in order.items.filter(deleted=False):
                self.perform_destroy(item)

            for activity in order.activities.filter(deleted=False):
                self.perform_destroy(activity)

            self.perform_destroy(order)

        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(
        detail=True,
        methods=["post"],
        url_path="send-classroom-invitations",
        permission_classes=[IsAuthenticated & IsStaffUser],
        serializer_class=None,
    )
    def send_classroom_invitations(self, request, pk=None):
        """
        Envía las invitaciones de Google Classroom para todos los items de la orden
        """
        try:
            order = self.get_object()
            result = process_classroom_invitation_for_order(str(order.oid), delay=False)

            success = result.get("success", None) == True

            if success:
                return Response(result, status=status.HTTP_200_OK)

            return Response(result, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                {"error": f"Error inesperado: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

from rest_framework import routers
from api.classroom.views import offering as offering_views
from api.classroom.views import auth as auth_views
from api.classroom.views import major as major_views
from api.classroom.views import term as term_views
from api.classroom.views import educational_institution as institution_views
from api.classroom.views import payment as class_payment_views

router = routers.DefaultRouter(trailing_slash=False)
router.register(
    r"offerings",
    offering_views.ClassroomOfferingViewset,
    basename="classroom-offerings",
)
router.register(
    r"user",
    auth_views.UserViewSet,
    basename="classroom-user",
)

router.register(
    r"major",
    major_views.MajorViewSet,
    basename="classroom-major",
)
router.register(
    r"term",
    term_views.TermViewSet,
    basename="classroom-term",
)
router.register(
    r"educational-institutions",
    institution_views.InstitutionViewSet,
    basename="classroom-institution",
)
router.register(
    r"payments",
    class_payment_views.ClassroomPaymentViewSet,
    basename="classroom-payments",
)
urlpatterns = router.urls
